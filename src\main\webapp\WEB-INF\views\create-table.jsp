<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Créer une table</title>
        <link rel="stylesheet" href="/css/dashboard.css">
        <link rel="stylesheet" href="/css/sidebar.css">
        <link rel="stylesheet" href="/css/create-database.css">
        <link rel="stylesheet" href="/css/messages.css">
        <style>
            .column-container {
                margin-top: 20px;
                border: 1px solid #ddd;
                padding: 15px;
                border-radius: 5px;
            }
            .column-row {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
                align-items: center;
            }
            .column-row input, .column-row select {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
            .add-column-btn {
                margin-top: 10px;
            }
            .remove-column-btn {
                background: #ff4d4d;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                cursor: pointer;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <jsp:include page="/WEB-INF/components/sidebar.jsp" />
            <div class="content">
                <jsp:include page="/WEB-INF/components/messages.jsp" />
                <h1>Créer une nouvelle table</h1>
                
                <form action="/create-table" method="post">
                    <input type="hidden" name="databaseId" value="<%= request.getAttribute("databaseId") %>">
                    <div>
                        <label for="tableName">Nom de la table:</label>
                        <input type="text" id="tableName" name="tableName" required>
                    </div>
                    
                    <div class="column-container">
                        <h3>Colonnes</h3>
                        <div id="columns-container">
                            <div class="column-row">
                                <input type="text" name="columnName[]" placeholder="Nom de colonne" required>
                                <select name="columnType[]">
                                    <option value="INT">INT</option>
                                    <option value="VARCHAR(255)">VARCHAR(255)</option>
                                    <option value="TEXT">TEXT</option>
                                    <option value="DATE">DATE</option>
                                    <option value="DATETIME">DATETIME</option>
                                    <option value="BOOLEAN">BOOLEAN</option>
                                    <option value="DECIMAL(10,2)">DECIMAL(10,2)</option>
                                </select>
                                <label>
                                    <input type="checkbox" name="columnPrimary[]" value="PRIMARY"> Clé primaire
                                </label>
                                <label>
                                    <input type="checkbox" name="columnNotNull[]" value="NOT_NULL"> NOT NULL
                                </label>
                                <label>
                                    <input type="checkbox" name="columnAutoIncrement[]" value="AUTO_INCREMENT"> Auto Increment
                                </label>
                                <button type="button" class="remove-column-btn" onclick="removeColumn(this)">Supprimer</button>
                            </div>
                        </div>
                        <button type="button" class="btn add-column-btn" onclick="addColumn()">+ Ajouter une colonne</button>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button type="submit" class="btn btn-primary">Créer</button>
                        <a href="/database?id=<%= request.getAttribute("databaseId") %>" class="btn">Annuler</a>
                    </div>
                </form>
            </div>
        </div>
        
        <script>
            function addColumn() {
                const container = document.getElementById('columns-container');
                const newRow = document.createElement('div');
                newRow.className = 'column-row';
                newRow.innerHTML = `
                    <input type="text" name="columnName[]" placeholder="Nom de colonne" required>
                    <select name="columnType[]">
                        <option value="INT">INT</option>
                        <option value="VARCHAR(255)">VARCHAR(255)</option>
                        <option value="TEXT">TEXT</option>
                        <option value="DATE">DATE</option>
                        <option value="DATETIME">DATETIME</option>
                        <option value="BOOLEAN">BOOLEAN</option>
                        <option value="DECIMAL(10,2)">DECIMAL(10,2)</option>
                    </select>
                    <label>
                        <input type="checkbox" name="columnPrimary[]" value="PRIMARY"> Clé primaire
                    </label>
                    <label>
                        <input type="checkbox" name="columnNotNull[]" value="NOT_NULL"> NOT NULL
                    </label>
                    <label>
                        <input type="checkbox" name="columnAutoIncrement[]" value="AUTO_INCREMENT"> Auto Increment
                    </label>
                    <button type="button" class="remove-column-btn" onclick="removeColumn(this)">Supprimer</button>
                `;
                container.appendChild(newRow);
            }
            
            function removeColumn(button) {
                const row = button.parentNode;
                if (document.querySelectorAll('.column-row').length > 1) {
                    row.parentNode.removeChild(row);
                } else {
                    alert('Vous devez avoir au moins une colonne');
                }
            }
        </script>
    </body>
</html>









