package com.my_project.controllers.database;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

@WebServlet("/database")
public class ViewDatabaseController extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        // Transférer les messages de session vers les attributs de requête
        transferSessionMessages(request);
        
        // Récupérer l'ID de la base de données
        String idParam = request.getParameter("id");
        if (idParam == null || idParam.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/dashboard");
            return;
        }
        
        try {
            // Récupérer les détails de la base de données
            Map<String, Object> database = getDatabaseDetails(Integer.parseInt(idParam));
            
            if (database != null) {
                // Récupérer les tables de cette base de données
                List<Map<String, Object>> tables = getDatabaseTables(Integer.parseInt(idParam));
                
                request.setAttribute("database", database);
                request.setAttribute("tables", tables);
                request.getRequestDispatcher("/WEB-INF/views/database-details.jsp").forward(request, response);
                return;
            }
        } catch (Exception e) {
            request.setAttribute("error", "Erreur: " + e.getMessage());
        }
        
        response.sendRedirect(request.getContextPath() + "/dashboard");
    }
    
    // Méthode pour transférer les messages de la session vers les attributs de requête
    private void transferSessionMessages(HttpServletRequest request) {
        if (request.getSession().getAttribute("message") != null) {
            request.setAttribute("message", request.getSession().getAttribute("message"));
            request.getSession().removeAttribute("message");
        }
        
        if (request.getSession().getAttribute("error") != null) {
            request.setAttribute("error", request.getSession().getAttribute("error"));
            request.getSession().removeAttribute("error");
        }
    }
    
    // Méthode pour récupérer les détails d'une base de données
    private Map<String, Object> getDatabaseDetails(int databaseId) throws SQLException {
        try (Connection connection = DatabaseConnection.getConnection()) {
            String sql = "SELECT * FROM `databases` WHERE id = ?";
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                statement.setInt(1, databaseId);
                
                try (var rs = statement.executeQuery()) {
                    if (rs.next()) {
                        Map<String, Object> database = new HashMap<>();
                        database.put("id", rs.getInt("id"));
                        database.put("name", rs.getString("name"));
                        database.put("created_at", rs.getTimestamp("created_at"));
                        return database;
                    }
                }
            }
        }
        return null;
    }

    // Méthode pour récupérer les tables d'une base de données
    private List<Map<String, Object>> getDatabaseTables(int databaseId) throws SQLException {
        List<Map<String, Object>> tables = new ArrayList<>();
        
        try (Connection connection = DatabaseConnection.getConnection()) {
            String sql = "SELECT * FROM `tables` WHERE database_id = ? ORDER BY name ASC";
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                statement.setInt(1, databaseId);
                try (var resultSet = statement.executeQuery()) {
                    while (resultSet.next()) {
                        Map<String, Object> table = new HashMap<>();
                        table.put("id", resultSet.getInt("id"));
                        table.put("name", resultSet.getString("name"));
                        table.put("created_at", resultSet.getTimestamp("created_at"));
                        tables.add(table);
                    }
                }
            }
        }
        
        return tables;
    }
}




