.database-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.database-title {
    font-size: 24px;
    margin: 0;
}

.database-actions {
    display: flex;
    gap: 10px;
}

.database-info {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.database-info p {
    margin: 5px 0;
}

.database-tables {
    margin-top: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.section-header h2 {
    margin: 0;
    font-size: 20px;
    color: #333;
    font-weight: 600;
}

.table-list {
    width: 100%;
    border-collapse: collapse;
}

.table-list th, 
.table-list td {
    padding: 14px 20px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table-list th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.table-list tr:hover {
    background-color: #f5f8ff;
}

.table-list tr:last-child td {
    border-bottom: none;
}

.table-list a {
    color: #2196F3;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.table-list a:hover {
    color: #0d8bf2;
    text-decoration: underline;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: #45a049;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 4px;
    background-color: #f0f0f0;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s;
}

.btn-sm:hover {
    background-color: #e0e0e0;
    text-decoration: none;
}

/* Styles pour l'aperçu de la table */
.table-preview {
    margin: 20px;
}

.table-preview-container {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: auto;
    max-width: 100%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
}

.preview-table th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
    white-space: nowrap;
    position: relative;
}

.preview-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    color: #999;
    text-align: center;
}

.column-type {
    display: block;
    font-weight: normal;
    color: #666;
    font-size: 11px;
    margin-top: 3px;
}

.column-primary {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    margin-left: 5px;
    vertical-align: middle;
}

/* Amélioration de la section des colonnes */
.table-columns {
    margin-top: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}


