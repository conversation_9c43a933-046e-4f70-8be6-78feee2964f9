<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.util.Map" %>

<div id="renameForm" class="rename-database-form">
    <h3>Renommer la base de données</h3>
    <form action="/rename-database" method="post">
        <input type="hidden" name="id" value="${database.get('id')}">
        <div class="form-group">
            <label for="newName">Nouveau nom:</label>
            <input type="text" id="newName" name="newName" value="${database.get('name')}" required>
        </div>
        <div class="form-actions">
            <button type="submit" class="btn btn-success">Enregistrer</button>
            <button type="button" onclick="hideRenameForm()" class="btn">Annuler</button>
        </div>
    </form>
</div>