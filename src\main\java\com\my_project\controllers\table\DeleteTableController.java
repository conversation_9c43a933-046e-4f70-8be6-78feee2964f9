package com.my_project.controllers.table;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;

@WebServlet("/delete-table")
public class DeleteTableController extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String tableId = request.getParameter("id");
        String databaseId = request.getParameter("databaseId");
        
        try {
            // Supprimer la table
            boolean success = deleteTable(Integer.parseInt(tableId));
            
            if (success) {
                request.getSession().setAttribute("message", "Table supprimée avec succès.");
            } else {
                request.getSession().setAttribute("error", "Impossible de supprimer la table.");
            }
        } catch (Exception e) {
            request.getSession().setAttribute("error", "Erreur: " + e.getMessage());
        }
        
        response.sendRedirect(request.getContextPath() + "/database?id=" + databaseId);
    }
    
    private boolean deleteTable(int id) {
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement("DELETE FROM `tables` WHERE id = ?")) {
            
            statement.setInt(1, id);
            return statement.executeUpdate() > 0;
            
        } catch (Exception e) {
            return false;
        }
    }
}