package com.my_project.controllers.table;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

@WebServlet("/table")
public class ViewTableController extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        // Transférer les messages de session vers les attributs de requête
        transferSessionMessages(request);
        
        // Récupérer l'ID de la table et de la base de données
        String tableId = request.getParameter("id");
        String databaseId = request.getParameter("databaseId");
        
        if (tableId == null || tableId.trim().isEmpty() || databaseId == null || databaseId.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/dashboard");
            return;
        }
        
        try {
            // Récupérer les détails de la table
            Map<String, Object> table = getTableDetails(Integer.parseInt(tableId));
            
            if (table != null) {
                // Récupérer les colonnes de cette table
                List<Map<String, Object>> columns = getTableColumns(Integer.parseInt(tableId));
                
                request.setAttribute("table", table);
                request.setAttribute("columns", columns);
                request.setAttribute("databaseId", databaseId);
                
                // Vérifier si le fichier JSP existe
                String jspPath = "/WEB-INF/views/table-details.jsp";
                if (getServletContext().getResource(jspPath) == null) {
                    System.out.println("ERREUR: Le fichier JSP n'existe pas: " + jspPath);
                    response.getWriter().println("ERREUR: Le fichier JSP n'existe pas: " + jspPath);
                    return;
                }
                
                request.getRequestDispatcher(jspPath).forward(request, response);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace(); // Ajouter cette ligne pour voir l'erreur complète
            request.setAttribute("error", "Erreur: " + e.getMessage());
        }
        
        response.sendRedirect(request.getContextPath() + "/database?id=" + databaseId);
    }
    
    // Méthode pour transférer les messages de la session vers les attributs de requête
    private void transferSessionMessages(HttpServletRequest request) {
        if (request.getSession().getAttribute("message") != null) {
            request.setAttribute("message", request.getSession().getAttribute("message"));
            request.getSession().removeAttribute("message");
        }
        
        if (request.getSession().getAttribute("error") != null) {
            request.setAttribute("error", request.getSession().getAttribute("error"));
            request.getSession().removeAttribute("error");
        }
    }
    
    // Méthode pour récupérer les détails d'une table
    private Map<String, Object> getTableDetails(int tableId) throws SQLException {
        try (Connection connection = DatabaseConnection.getConnection()) {
            String sql = "SELECT t.*, d.name as database_name FROM `tables` t " +
                         "JOIN `databases` d ON t.database_id = d.id " +
                         "WHERE t.id = ?";
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                statement.setInt(1, tableId);
                
                try (var rs = statement.executeQuery()) {
                    if (rs.next()) {
                        Map<String, Object> table = new HashMap<>();
                        table.put("id", rs.getInt("id"));
                        table.put("name", rs.getString("name"));
                        table.put("database_id", rs.getInt("database_id"));
                        table.put("database_name", rs.getString("database_name"));
                        table.put("created_at", rs.getTimestamp("created_at"));
                        return table;
                    }
                }
            }
        }
        return null;
    }

    // Méthode pour récupérer les colonnes d'une table
    private List<Map<String, Object>> getTableColumns(int tableId) throws SQLException {
        List<Map<String, Object>> columns = new ArrayList<>();
        
        try (Connection connection = DatabaseConnection.getConnection()) {
            String sql = "SELECT * FROM `columns` WHERE table_id = ? ORDER BY id ASC";
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                statement.setInt(1, tableId);
                try (var resultSet = statement.executeQuery()) {
                    while (resultSet.next()) {
                        Map<String, Object> column = new HashMap<>();
                        column.put("id", resultSet.getInt("id"));
                        column.put("name", resultSet.getString("name"));
                        column.put("type", resultSet.getString("type"));
                        column.put("is_primary", resultSet.getBoolean("is_primary"));
                        column.put("is_not_null", resultSet.getBoolean("is_not_null"));
                        column.put("is_auto_increment", resultSet.getBoolean("is_auto_increment"));
                        columns.add(column);
                    }
                }
            }
        }
        
        return columns;
    }
}





