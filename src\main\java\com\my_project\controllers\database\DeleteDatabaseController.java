package com.my_project.controllers.database;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;

@WebServlet("/delete-database")
public class DeleteDatabaseController extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String idParam = request.getParameter("id");
        
        try {
            // Supprimer la base de données
            int databaseId = Integer.parseInt(idParam);
            boolean success = supprimerDatabase(databaseId);
            
            if (success) {
                request.getSession().setAttribute("message", "Base de données supprimée avec succès.");
            } else {
                request.getSession().setAttribute("error", "Impossible de supprimer la base de données.");
            }
        } catch (Exception e) {
            request.getSession().setAttribute("error", "Erreur: " + e.getMessage());
        }
        
        response.sendRedirect(request.getContextPath() + "/dashboard");
    }
    
    private boolean supprimerDatabase(int id) {
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement("DELETE FROM `databases` WHERE id = ?")) {
            
            statement.setInt(1, id);
            return statement.executeUpdate() > 0;
            
        } catch (Exception e) {
            return false;
        }
    }
}
