<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.util.Map" %>

<div id="renameTableForm" class="rename-table-form">
    <h3>Renommer la table</h3>
    <form action="/rename-table" method="post">
        <input type="hidden" name="id" value="${table.get('id')}">
        <input type="hidden" name="databaseId" value="${requestScope.databaseId}">
        <div class="form-group">
            <label for="newName">Nouveau nom:</label>
            <input type="text" id="newName" name="newName" value="${table.get('name')}" required>
        </div>
        <div class="form-actions">
            <button type="submit" class="btn btn-success">Enregistrer</button>
            <button type="button" onclick="hideRenameTableForm()" class="btn">Annuler</button>
        </div>
    </form>
</div>