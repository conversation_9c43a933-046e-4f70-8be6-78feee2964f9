package com.my_project.filters;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@WebFilter("/*")
public class DatabaseListFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        // Ne charger les bases de données que si l'utilisateur est connecté
        if (httpRequest.getSession().getAttribute("user") != null) {
            try (Connection conn = DatabaseConnection.getConnection()) {
                String sql = "SELECT id, name FROM `databases` ORDER BY name ASC";
                try (PreparedStatement stmt = conn.prepareStatement(sql);
                     var rs = stmt.executeQuery()) {
                    
                    List<Map<String, Object>> databases = new ArrayList<>();
                    while (rs.next()) {
                        Map<String, Object> db = new HashMap<>();
                        db.put("id", rs.getInt("id"));
                        db.put("name", rs.getString("name"));
                        databases.add(db);
                    }
                    
                    request.setAttribute("databases", databases);
                }
            } catch (SQLException e) {
                // Juste logger l'erreur, ne pas interrompre la chaîne de filtres
                System.err.println("Erreur lors de la récupération des bases de données: " + e.getMessage());
            }
        }
        
        chain.doFilter(request, response);
    }
}