package com.my_project.controllers.table;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

@WebServlet("/create-table")
public class CreateTableController extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String databaseId = request.getParameter("databaseId");
        if (databaseId == null || databaseId.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/dashboard");
            return;
        }
        
        request.setAttribute("databaseId", databaseId);
        request.getRequestDispatcher("/WEB-INF/views/create-table.jsp").forward(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String databaseId = request.getParameter("databaseId");
        String tableName = request.getParameter("tableName");
        String[] columnNames = request.getParameterValues("columnName[]");
        String[] columnTypes = request.getParameterValues("columnType[]");
        String[] columnPrimaries = request.getParameterValues("columnPrimary[]");
        String[] columnNotNulls = request.getParameterValues("columnNotNull[]");
        String[] columnAutoIncrements = request.getParameterValues("columnAutoIncrement[]");
        
        try {
            if (createTable(Integer.parseInt(databaseId), tableName, columnNames, columnTypes, 
                    columnPrimaries, columnNotNulls, columnAutoIncrements)) {
                request.getSession().setAttribute("message", "Table '" + tableName + "' créée avec succès!");
                response.sendRedirect(request.getContextPath() + "/database?id=" + databaseId);
            } else {
                request.setAttribute("error", "La table '" + tableName + "' existe déjà.");
                request.setAttribute("databaseId", databaseId);
                request.getRequestDispatcher("/WEB-INF/views/create-table.jsp").forward(request, response);
            }
        } catch (SQLException e) {
            request.setAttribute("error", "Erreur: " + e.getMessage());
            request.setAttribute("databaseId", databaseId);
            request.getRequestDispatcher("/WEB-INF/views/create-table.jsp").forward(request, response);
        }
    }
    
    private boolean createTable(int databaseId, String tableName, String[] columnNames, String[] columnTypes,
                               String[] columnPrimaries, String[] columnNotNulls, String[] columnAutoIncrements) throws SQLException {
        try (Connection connection = DatabaseConnection.getConnection()) {
            // Créer la table tables si nécessaire
            connection.prepareStatement(
                "CREATE TABLE IF NOT EXISTS `tables` (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "database_id INT NOT NULL, " +
                "name VARCHAR(255) NOT NULL, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (database_id) REFERENCES `databases`(id) ON DELETE CASCADE, " +
                "UNIQUE KEY unique_table_name (database_id, name))"
            ).executeUpdate();
            
            // Vérifier si le nom existe déjà pour cette base de données
            PreparedStatement check = connection.prepareStatement(
                "SELECT COUNT(*) FROM `tables` WHERE database_id = ? AND name = ?"
            );
            check.setInt(1, databaseId);
            check.setString(2, tableName);
            var result = check.executeQuery();
            if (result.next() && result.getInt(1) > 0) {
                return false; 
            }
            
            // Créer la table pour stocker les colonnes si nécessaire
            connection.prepareStatement(
                "CREATE TABLE IF NOT EXISTS `columns` (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "table_id INT NOT NULL, " +
                "name VARCHAR(255) NOT NULL, " +
                "type VARCHAR(255) NOT NULL, " +
                "is_primary BOOLEAN DEFAULT FALSE, " +
                "is_not_null BOOLEAN DEFAULT FALSE, " +
                "is_auto_increment BOOLEAN DEFAULT FALSE, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (table_id) REFERENCES `tables`(id) ON DELETE CASCADE, " +
                "UNIQUE KEY unique_column_name (table_id, name))"
            ).executeUpdate();
            
            // Insérer la nouvelle table
            PreparedStatement insertTable = connection.prepareStatement(
                "INSERT INTO `tables` (database_id, name) VALUES (?, ?)",
                PreparedStatement.RETURN_GENERATED_KEYS
            );
            insertTable.setInt(1, databaseId);
            insertTable.setString(2, tableName);
            insertTable.executeUpdate();
            
            // Récupérer l'ID de la table créée
            var tableKeys = insertTable.getGeneratedKeys();
            if (!tableKeys.next()) {
                throw new SQLException("Impossible de récupérer l'ID de la table créée");
            }
            int tableId = tableKeys.getInt(1);
            
            // Ajouter chaque colonne dans la table columns
            for (int i = 0; i < columnNames.length; i++) {
                String columnName = columnNames[i];
                String columnType = columnTypes[i];
                
                // Vérifier si les cases sont cochées pour cette colonne
                boolean isPrimary = false;
                boolean isNotNull = false;
                boolean isAutoIncrement = false;
                
                // Vérifier si la colonne est une clé primaire
                if (columnPrimaries != null) {
                    for (int j = 0; j < columnPrimaries.length; j++) {
                        if (j == i) {
                            isPrimary = true;
                            break;
                        }
                    }
                }
                
                // Vérifier si la colonne est NOT NULL
                if (columnNotNulls != null) {
                    for (int j = 0; j < columnNotNulls.length; j++) {
                        if (j == i) {
                            isNotNull = true;
                            break;
                        }
                    }
                }
                
                // Vérifier si la colonne est AUTO INCREMENT
                if (columnAutoIncrements != null) {
                    for (int j = 0; j < columnAutoIncrements.length; j++) {
                        if (j == i) {
                            isAutoIncrement = true;
                            break;
                        }
                    }
                }
                
                // Insérer les informations de colonne dans la table columns
                PreparedStatement insertColumn = connection.prepareStatement(
                    "INSERT INTO `columns` (table_id, name, type, is_primary, is_not_null, is_auto_increment) VALUES (?, ?, ?, ?, ?, ?)"
                );
                insertColumn.setInt(1, tableId);
                insertColumn.setString(2, columnName);
                insertColumn.setString(3, columnType);
                insertColumn.setBoolean(4, isPrimary);
                insertColumn.setBoolean(5, isNotNull);
                insertColumn.setBoolean(6, isAutoIncrement);
                insertColumn.executeUpdate();
            }
            
            return true;
        }
    }
}








