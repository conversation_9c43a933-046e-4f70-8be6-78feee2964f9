package com.my_project.controllers.database;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;

@WebServlet("/rename-database")
public class RenameDatabaseController extends HttpServlet {
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String id = request.getParameter("id");
        String newName = request.getParameter("newName");
        
        try {
            int databaseId = Integer.parseInt(id);
            boolean success = renameDatabase(databaseId, newName);
            
            if (success) {
                request.getSession().setAttribute("message", "Base de données renommée avec succès.");
            } else {
                request.getSession().setAttribute("error", "Impossible de renommer la base de données.");
            }
        } catch (Exception e) {
            request.getSession().setAttribute("error", "Erreur: " + e.getMessage());
        }
        
        response.sendRedirect(request.getContextPath() + "/database?id=" + id);
    }
    
    private boolean renameDatabase(int id, String newName) {
        if (newName == null || newName.trim().isEmpty()) {
            return false;
        }
        
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement("UPDATE `databases` SET name = ? WHERE id = ?")) {
            
            statement.setString(1, newName);
            statement.setInt(2, id);
            
            return statement.executeUpdate() > 0;
            
        } catch (Exception e) {
            return false;
        }
    }
}

