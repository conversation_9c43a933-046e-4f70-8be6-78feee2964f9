package com.my_project.controllers.database;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

@WebServlet("/create-database")
public class CreateDatabaseController extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        request.getRequestDispatcher("/WEB-INF/views/create-database.jsp").forward(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String dbName = request.getParameter("dbName");
        
        try {
            if (createDatabase(dbName)) {
                request.getSession().setAttribute("message", "Base de données '" + dbName + "' créée!");
                response.sendRedirect(request.getContextPath() + "/dashboard");
            } else {
                request.setAttribute("error", "La base de données '" + dbName + "' existe déjà.");
                request.getRequestDispatcher("/WEB-INF/views/create-database.jsp").forward(request, response);
            }
        } catch (SQLException e) {
            request.setAttribute("error", "Erreur: " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/create-database.jsp").forward(request, response);
        }
    }
    
    private boolean createDatabase(String dbName) throws SQLException {
        try (Connection connection = DatabaseConnection.getConnection()) {
            // Créer la table si nécessaire
            connection.prepareStatement(
                "CREATE TABLE IF NOT EXISTS `databases` (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(255) NOT NULL UNIQUE, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)"
            ).executeUpdate();
            
            // Vérifier si le nom existe déjà
            PreparedStatement check = connection.prepareStatement("SELECT COUNT(*) FROM `databases` WHERE name = ?");
            check.setString(1, dbName);
            var result = check.executeQuery();
            if (result.next() && result.getInt(1) > 0) {
                return false; 
            }
            
            // Insérer le nouveau nom
            PreparedStatement insert = connection.prepareStatement("INSERT INTO `databases` (name) VALUES (?)");
            insert.setString(1, dbName);
            insert.executeUpdate();
            return true;
        }
    }
}



