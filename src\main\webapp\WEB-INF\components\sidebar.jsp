<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.util.List, java.util.Map" %>
<div class="sidebar">
    <h2>Menu SGBD</h2>
    <ul class="sidebar-menu">
        <li><a href="/logout">Déconnexion</a></li>
        <li><a href="/create-database">Créer une base de données</a></li>
        
        <% 
        List<Map<String, Object>> databases = (List<Map<String, Object>>) request.getAttribute("databases");
        if (databases != null && !databases.isEmpty()) {
        %>
            <li class="sidebar-submenu">
                <span>Bases de données</span>
                <ul>
                    <% for (Map<String, Object> db : databases) { %>
                        <li><a href="/database?id=<%= db.get("id") %>"><%= db.get("name") %></a></li>
                    <% } %>
                </ul>
            </li>
        <% } %>
    </ul>
</div>




