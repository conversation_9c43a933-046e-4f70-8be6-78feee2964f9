<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.util.Map, java.sql.Timestamp, java.text.SimpleDateFormat, java.util.List" %>
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Détails de la base de données</title>
        <link rel="stylesheet" href="/css/dashboard.css">
        <link rel="stylesheet" href="/css/sidebar.css">
        <link rel="stylesheet" href="/css/database-details.css">
        <link rel="stylesheet" href="/css/rename-database-form.css">
        <link rel="stylesheet" href="/css/messages.css">
    </head>
    <body>
        <div class="container">
            <jsp:include page="/WEB-INF/components/sidebar.jsp" />
            <div class="content">
                <jsp:include page="/WEB-INF/components/messages.jsp" />
                <% 
                Map<String, Object> database = (Map<String, Object>) request.getAttribute("database");
                if (database != null) {
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                %>
                    <div class="database-header">
                        <h1 class="database-title">Base de données: <%= database.get("name") %></h1>
                        <div class="database-actions">
                            <button onclick="showRenameForm()" class="btn btn-primary">Renommer</button>
                            <button onclick="confirmDelete(`<%= database.get("id") %>`)" class="btn btn-danger">Supprimer</button>
                        </div>
                    </div>
                    
                    <jsp:include page="/WEB-INF/components/rename-database-form.jsp" />
                    
                    <div class="database-tables">
                        <div class="section-header">
                            <h2>Tables</h2>
                            <a href="/create-table?databaseId=<%= database.get("id") %>" class="btn btn-primary">Ajouter une table</a>
                        </div>
                        <% 
                        List<Map<String, Object>> tables = (List<Map<String, Object>>) request.getAttribute("tables");
                        if (tables != null && !tables.isEmpty()) {
                        %>
                            <table class="table-list">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Date de création</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% for (Map<String, Object> table : tables) { %>
                                        <tr>
                                            <td><a href="<%= request.getContextPath() %>/table?id=<%= table.get("id") %>&databaseId=<%= database.get("id") %>"><%= table.get("name") %></a></td>
                                            <td><%= new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(table.get("created_at")) %></td>
                                            <td>
                                                <a href="<%= request.getContextPath() %>/table?id=<%= table.get("id") %>&databaseId=<%= database.get("id") %>" class="btn btn-sm">Voir</a>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        <% } else { %>
                            <div style="padding: 20px; text-align: center; color: #666;">
                                <p>Aucune table n'a été créée pour cette base de données.</p>
                            </div>
                        <% } %>
                    </div>
                <% } else { %>
                    <h1>Base de données non trouvée</h1>
                    <p>La base de données demandée n'existe pas ou a été supprimée.</p>
                <% } %>
            </div>
        </div>
        
        <script src="/js/rename-database-form.js"></script>
        <script>
            function confirmDelete(id) {
                if (confirm('Êtes-vous sûr de vouloir supprimer cette base de données? Cette action est irréversible.')) {
                    window.location.href = '/delete-database?id=' + id;
                }
            }
        </script>
    </body>
</html>





