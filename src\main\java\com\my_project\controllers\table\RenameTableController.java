package com.my_project.controllers.table;

import com.my_project.utils.DatabaseConnection;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;

@WebServlet("/rename-table")
public class RenameTableController extends HttpServlet {
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        if (request.getSession().getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        String tableId = request.getParameter("id");
        String databaseId = request.getParameter("databaseId");
        String newName = request.getParameter("newName");
        
        try {
            boolean success = renameTable(Integer.parseInt(tableId), newName);
            
            if (success) {
                request.getSession().setAttribute("message", "Table renommée avec succès.");
            } else {
                request.getSession().setAttribute("error", "Impossible de renommer la table.");
            }
        } catch (Exception e) {
            request.getSession().setAttribute("error", "Erreur: " + e.getMessage());
        }
        
        response.sendRedirect(request.getContextPath() + "/table?id=" + tableId + "&databaseId=" + databaseId);
    }
    
    private boolean renameTable(int id, String newName) {
        if (newName == null || newName.trim().isEmpty()) {
            return false;
        }
        
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement("UPDATE `tables` SET name = ? WHERE id = ?")) {
            
            statement.setString(1, newName);
            statement.setInt(2, id);
            
            return statement.executeUpdate() > 0;
            
        } catch (Exception e) {
            return false;
        }
    }
}
