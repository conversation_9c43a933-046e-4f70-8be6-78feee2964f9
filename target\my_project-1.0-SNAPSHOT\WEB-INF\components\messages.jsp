<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% if(request.getAttribute("message") != null) { %>
    <div id="successMessage" class="alert alert-success">
        <%= request.getAttribute("message") %>
    </div>
<% } %>

<% if(request.getAttribute("error") != null) { %>
    <div id="errorMessage" class="alert alert-danger">
        <%= request.getAttribute("error") %>
    </div>
<% } %>

<script>
    // Faire disparaître les messages après 5 secondes
    setTimeout(function() {
        var successMessage = document.getElementById('successMessage');
        if (successMessage) {
            successMessage.style.transition = 'opacity 1s';
            successMessage.style.opacity = '0';
            setTimeout(function() {
                successMessage.style.display = 'none';
            }, 1000);
        }
        
        var errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            errorMessage.style.transition = 'opacity 1s';
            errorMessage.style.opacity = '0';
            setTimeout(function() {
                errorMessage.style.display = 'none';
            }, 1000);
        }
    }, 2000); // 5000 ms = 5 secondes
</script>
