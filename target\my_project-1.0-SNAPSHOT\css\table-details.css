/* Styles pour la page de détails de table */

/* En-tête de la table */
.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.table-title {
    font-size: 24px;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 10px;
}

/* Informations de la table */
.table-info {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.table-info p {
    margin: 5px 0;
}

/* Section des colonnes */
.table-columns {
    margin-top: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Aperçu de la table */
.table-preview {
    margin: 20px;
}

.table-preview-container {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: auto;
    max-width: 100%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
}

.preview-table th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
    white-space: nowrap;
    position: relative;
}

.preview-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    color: #999;
    text-align: center;
}

.column-type {
    display: block;
    font-weight: normal;
    color: #666;
    font-size: 11px;
    margin-top: 3px;
}

.column-primary {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    margin-left: 5px;
    vertical-align: middle;
}

/* Détails des colonnes */
.column-details {
    margin: 20px;
}

.column-list {
    width: 100%;
    border-collapse: collapse;
}

.column-list th, 
.column-list td {
    padding: 14px 20px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.column-list th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.column-list tr:hover {
    background-color: #f5f8ff;
}

.column-list tr:last-child td {
    border-bottom: none;
}

/* Badges pour les attributs de colonne */
.badge {
    display: inline-block;
    padding: 3px 6px;
    font-size: 11px;
    font-weight: 500;
    border-radius: 3px;
    margin-right: 5px;
    color: white;
}

.badge-primary {
    background-color: #4CAF50;
}

.badge-info {
    background-color: #2196F3;
}

.badge-success {
    background-color: #FF9800;
}

/* Message quand aucune colonne n'est définie */
.no-columns {
    padding: 30px;
    text-align: center;
    color: #666;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.no-columns p:first-child {
    font-size: 18px;
    margin-bottom: 10px;
}

.no-columns p:last-child {
    color: #999;
}

/* Lien de retour */
.back-link {
    margin-top: 30px;
}

.back-icon {
    margin-right: 5px;
}

/* Conteneur d'erreur */
.error-container {
    text-align: center;
    padding: 50px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.error-container h1 {
    color: #dc3545;
    margin-bottom: 20px;
}

.error-container p {
    color: #6c757d;
    margin-bottom: 30px;
}

/* Boutons spécifiques */
.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-edit {
    background-color: #17a2b8;
    color: white;
}

.btn-edit:hover {
    background-color: #138496;
}

/* Responsive design */
@media (max-width: 768px) {
    .table-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .table-actions {
        margin-top: 15px;
    }
    
    .column-list th, 
    .column-list td {
        padding: 10px 15px;
    }
}
