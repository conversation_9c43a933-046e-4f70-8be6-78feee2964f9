<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.util.Map, java.util.List, java.sql.Timestamp, java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Détails de la table</title>
        <link rel="stylesheet" href="<%= request.getContextPath() %>/css/dashboard.css">
        <link rel="stylesheet" href="<%= request.getContextPath() %>/css/sidebar.css">
        <link rel="stylesheet" href="<%= request.getContextPath() %>/css/table-details.css">
        <link rel="stylesheet" href="<%= request.getContextPath() %>/css/messages.css">
        <link rel="stylesheet" href="<%= request.getContextPath() %>/css/rename-table-form.css">
    </head>
    <body>
        <div class="container">
            <jsp:include page="/WEB-INF/components/sidebar.jsp" />
            <div class="content">
                <jsp:include page="/WEB-INF/components/messages.jsp" />
                <% 
                Map<String, Object> table = (Map<String, Object>) request.getAttribute("table");
                if (table != null) {
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                %>
                    <div class="table-header">
                        <h1 class="table-title">Table: <%= table.get("name") %></h1>
                        <div class="table-actions">
                            <button onclick="showRenameTableForm()" class="btn btn-edit">Renommer</button>
                            <button onclick="confirmDeleteTable(<%= table.get("id") %>, <%= request.getAttribute("databaseId") %>)" class="btn btn-danger">Supprimer</button>
                        </div>
                    </div>

                    <jsp:include page="/WEB-INF/components/rename-table-form.jsp" />             
                    
                    <div class="table-columns">
                        <div class="section-header">
                            <h2>Colonnes</h2>
                            <a href="<%= request.getContextPath() %>/add-column?tableId=<%= table.get("id") %>&databaseId=<%= request.getAttribute("databaseId") %>" class="btn btn-primary">Ajouter une colonne</a>
                        </div>
                        <% 
                        List<Map<String, Object>> columns = (List<Map<String, Object>>) request.getAttribute("columns");
                        if (columns != null && !columns.isEmpty()) {
                        %>
                            <div class="table-preview">
                                <div class="table-preview-container">
                                    <table class="preview-table">
                                        <thead>
                                            <tr>
                                                <% for (Map<String, Object> column : columns) { %>
                                                    <th>
                                                        <%= column.get("name") %>
                                                        <span class="column-type"><%= column.get("type") %></span>
                                                        <% if ((Boolean)column.get("is_primary")) { %>
                                                            <span class="column-primary">PK</span>
                                                        <% } %>
                                                    </th>
                                                <% } %>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <% for (int i = 0; i < columns.size(); i++) { %>
                                                    <td>...</td>
                                                <% } %>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="column-details">
                                <table class="column-list">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Type</th>
                                            <th>Attributs</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% for (Map<String, Object> column : columns) { %>
                                            <tr>
                                                <td><%= column.get("name") %></td>
                                                <td><%= column.get("type") %></td>
                                                <td>
                                                    <% if ((Boolean)column.get("is_primary")) { %>
                                                        <span class="badge badge-primary">Clé primaire</span>
                                                    <% } %>
                                                    <% if ((Boolean)column.get("is_not_null")) { %>
                                                        <span class="badge badge-info">NOT NULL</span>
                                                    <% } %>
                                                    <% if ((Boolean)column.get("is_auto_increment")) { %>
                                                        <span class="badge badge-success">AUTO_INCREMENT</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <a href="<%= request.getContextPath() %>/edit-column?id=<%= column.get("id") %>&tableId=<%= table.get("id") %>&databaseId=<%= request.getAttribute("databaseId") %>" class="btn btn-sm">Modifier</a>
                                                    <button onclick="confirmDeleteColumn(<%= column.get("id") %>, <%= table.get("id") %>, <%= request.getAttribute("databaseId") %>)" class="btn btn-sm btn-danger">Supprimer</button>
                                                </td>
                                            </tr>
                                        <% } %>
                                    </tbody>
                                </table>
                            </div>
                        <% } else { %>
                            <div class="no-columns">
                                <p>Aucune colonne n'a été définie pour cette table.</p>
                                <p>Cliquez sur "Ajouter une colonne" pour commencer à définir la structure de votre table.</p>
                            </div>
                        <% } %>
                    </div>
                    
                    <div class="back-link">
                        <a href="<%= request.getContextPath() %>/database?id=<%= request.getAttribute("databaseId") %>" class="btn btn-sm">
                            <span class="back-icon">←</span> Retour à la base de données
                        </a>
                    </div>
                <% } else { %>
                    <div class="error-container">
                        <h1>Table non trouvée</h1>
                        <p>La table demandée n'existe pas ou a été supprimée.</p>
                        <a href="<%= request.getContextPath() %>/database?id=<%= request.getAttribute("databaseId") %>" class="btn btn-primary">
                            Retour à la base de données
                        </a>
                    </div>
                <% } %>
            </div>
        </div>
        
        <script src="<%= request.getContextPath() %>/js/rename-table-form.js"></script>
        <script>
            function confirmDeleteTable(tableId, databaseId) {
                if (confirm("Êtes-vous sûr de vouloir supprimer cette table ? Cette action est irréversible.")) {
                    window.location.href = "<%= request.getContextPath() %>/delete-table?id=" + tableId + "&databaseId=" + databaseId;
                }
            }
            
            function confirmDeleteColumn(columnId, tableId, databaseId) {
                if (confirm("Êtes-vous sûr de vouloir supprimer cette colonne ? Cette action est irréversible.")) {
                    window.location.href = "<%= request.getContextPath() %>/delete-column?id=" + columnId + "&tableId=" + tableId + "&databaseId=" + databaseId;
                }
            }
        </script>
    </body>
</html>







